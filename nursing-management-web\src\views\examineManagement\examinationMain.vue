<!--
 * FilePath     : \src\views\examineManagement\examinationMain.vue
 * Author       : 来江禹
 * Date         : 2024-08-24 08:10
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-20 10:14
 * Description  : 考核计划汇总页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="examination-plan-summary" showFooter :drawerOptions="drawerOptions">
    <template #header>
      日期：
      <el-date-picker
        v-model="filterDate"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="header-date"
        @change="getPlanSummaryList()"
      />
      <examination-type-radio v-model="examinationType" type="Examination" @change="getPlanSummaryList"></examination-type-radio>
      <span>
        监考人：
        <employee-selector label="" v-model="invigilatorEmployeeID" :filterable="true" :clearable="true" @change="getPlanSummaryList()" />
      </span>
      <export-excel class="export-excel" :exportExcelOption="exportExcelOption">
        <el-button class="print-button" @click="createExportExcelParam">导出考核计划</el-button>
      </export-excel>
    </template>
    <el-table
      ref="planSummaryTableRef"
      :data="planSummaryList"
      border
      stripe
      height="100%"
      :row-key="examinationType === '1' ? 'groupID' : 'examinationRecordID'"
    >
      <template v-if="examinationType === '1'">
        <!-- 展开列 -->
        <el-table-column type="expand" width="50">
          <template #default="{ row }">
            <div class="expand-content" v-if="row.children">
              <h4>{{ row.groupName }} - 分批考核明细</h4>
              <el-table :data="row.children" border size="small" class="expand-table">
                <el-table-column label="批次" width="80" align="center">
                  <template #default="{ row: childRow }">
                    <span>第{{ childRow.batchNumber }}批</span>
                  </template>
                </el-table-column>
                <el-table-column label="考核名称" prop="examineName" :min-width="convertPX(160)"></el-table-column>
                <el-table-column label="考核级别" prop="examinationLevelName" :width="convertPX(100)" align="center"></el-table-column>
                <el-table-column label="监考人" prop="examinerName" :width="convertPX(100)" align="center"></el-table-column>
                <el-table-column label="开始时间" prop="startDateTime" :width="convertPX(160)" align="center"></el-table-column>
                <el-table-column label="结束时间" prop="endDateTime" :width="convertPX(160)" align="center"></el-table-column>
                <el-table-column label="应参加人数" prop="shouldParticipateCount" :width="convertPX(100)" align="center"></el-table-column>
                <el-table-column label="参加人数" prop="participateCount" :width="convertPX(100)" align="center"></el-table-column>
                <el-table-column label="未参加人数" prop="notParticipateCount" :width="convertPX(100)" align="center"></el-table-column>
                <el-table-column
                  :label="examinationType === '1' ? '及格人数' : '达标人数'"
                  prop="passCount"
                  width="100"
                  align="center"
                ></el-table-column>
                <el-table-column label="操作" width="120" align="center">
                  <template #default="{ row: childRow }">
                    <el-tooltip content="查看详情" placement="top">
                      <i class="iconfont icon-info" @click="openDetailDrawer(childRow)"></i>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="考核名称" prop="groupName" min-width="200" align="left"></el-table-column>
      <el-table-column :width="convertPX(120)" label="考核级别" prop="examinationLevelName" align="center"></el-table-column>
      <el-table-column :width="convertPX(100)" label="监考人" prop="examinerName" align="center"></el-table-column>
      <el-table-column :width="convertPX(100)" label="应参加人数" prop="shouldParticipateCount" align="center"></el-table-column>
      <el-table-column :width="convertPX(100)" label="参加人数" prop="participateCount" align="center"></el-table-column>
      <el-table-column :width="convertPX(100)" label="未参加人数" prop="notParticipateCount" align="center"></el-table-column>
      <el-table-column :width="convertPX(120)" :label="examinationType === '1' ? '及格人数' : '达标人数'" prop="passCount" align="center">
        <template #default="{ row }">
          <span>{{ row.passCount }}</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(100)" label="参考率(%)" prop="participationRate" align="center">
        <template #default="{ row }">
          <span>{{ row.participationRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(100)" label="合格率(%)" prop="passRate" align="center">
        <template #default="{ row }">
          <span>{{ row.passRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(120)" label="集中考核参加率(%)" prop="centralExamParticipationRate" align="center">
        <template #default="{ row }">
          <span>{{ row.centralExamParticipationRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(120)" label="首次考核合格率(%)" prop="firstExamPassRate" align="center">
        <template #default="{ row }">
          <span>{{ row.firstExamPassRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(160)" align="center" fixed="right">
        <template #default="{ row }">
          <el-tooltip content="查看详情" placement="top" v-if="examinationType === '2'">
            <i class="iconfont icon-info" @click="openDetailDrawer(row)"></i>
          </el-tooltip>
          <el-tooltip content="批量补考" placement="top">
            <i class="iconfont icon-supplement" v-permission:B="1" @click="openBatchRetakeDialog(row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        small
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[25, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableDataCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
    <template #drawerContent>
      <examination-main-detail
        v-if="drawerOptions.drawerName == 'examinationMainDetail'"
        :examineRecordID="selectedPlanRecord?.examinationRecordID"
        :examinationType="selectedPlanRecord?.examinationType"
      />
      <el-form
        v-if="drawerOptions.drawerName == 'retakeExamine'"
        :model="retakeExamine"
        ref="retakeExamineFormRef"
        label-width="80"
        :rules="rules"
      >
        <el-form-item label="开始时间：" prop="startDateTime">
          <el-date-picker
            class="drawer-select"
            v-model="retakeExamine.startDateTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            placeholder="选择开始时间"
            :disabled-date="(date: Date) => disabledDate(date, 'start')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间：" prop="endDateTime">
          <el-date-picker
            class="drawer-select"
            v-model="retakeExamine.endDateTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            placeholder="选择结束时间"
            :disabled-date="(date: Date) => disabledDate(date, 'end')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="补考人员：" prop="employeeIDs">
          <employee-selector
            label=""
            v-model="retakeExamine.employeeIDs"
            :width="320"
            :filterable="true"
            :multiple="true"
            :multiCollapse="false"
            :clear="false"
          />
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
// #region 引入
import ExaminationMainDetail from "./examinationMainDetail.vue";
let { userStore } = useStore();
const convertPX: any = inject("convertPX");
// #endregion

const props = defineProps({
  examineRecordID: {
    type: String,
    default: undefined
  },
  examinationType: {
    type: String,
    default: ""
  }
});

// #region 定义变量
const examinationType = ref<string>(props.examinationType || "1");
const filterDate = ref<string[]>([datetimeUtil.getMonthFirstDay(), datetimeUtil.getMonthLastDay()]);
const invigilatorEmployeeID = ref<string>(userStore.employeeID);
const pageSize = ref<number>(30);
const tableDataCount = ref<number>(0);
const currentPage = ref<number>(1);
const planSummaryTableRef = ref<any>();
const planSummaryList = ref<Record<string, any>[]>([]);
const allPlanSummaryList = ref<Record<string, any>[]>([]);
const selectedPlanRecord = ref<Record<string, any> | undefined>(undefined);

// 批量补考相关数据
const currentExamGroup = ref<Record<string, any> | undefined>(undefined);
const retakeExamine = ref<Record<string, any>>({
  startDateTime: "",
  endDateTime: "",
  employeeIDs: []
});
const retakeExamineFormRef = shallowRef();
const { validateRule } = useForm();

// 补考人员相关数据（使用employee-selector组件，不需要额外的状态管理）

// 表单验证规则
const rules = {
  startDateTime: [{ required: true, message: "请选择补考开始时间", trigger: "change" }],
  endDateTime: [{ required: true, message: "请选择补考结束时间", trigger: "change" }]
};
// #endregion

// #region 初始化
onMounted(() => {
  getPlanSummaryList();
});
// #endregion

// #region 抽屉配置
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "考核记录详情",
  showDrawer: false,
  drawerSize: "90%",
  showConfirm: false,
  showCancel: false,
  drawerName: ""
});
// #endregion

// #region 业务逻辑
/**
 * @description: 获取考核计划汇总数据
 * @return
 */
const getPlanSummaryList = () => {
  const params = {
    invigilatorEmployeeID: invigilatorEmployeeID.value,
    startDate: filterDate.value[0],
    endDate: filterDate.value[1],
    examinationType: examinationType.value
  };

  examineService.getExaminationSummaryList(params).then((res: any) => {
    const data = res ?? [];
    processPlanSummaryData(data);
  });
};

/**
 * @description: 处理考核计划汇总数据
 * @param data 原始数据
 */
const processPlanSummaryData = (data: Record<string, any>[]) => {
  allPlanSummaryList.value = data;

  // 分页处理
  tableDataCount.value = data.length;
  planSummaryList.value = [];
  planSummaryTableRef.value?.doLayout();
  planSummaryList.value = currentChangePage();
};

/**
 * @description: 检查批次状态，判断是否可以进行补考
 * @param examGroup 考核组数据
 * @return 检查结果
 */
const checkBatchStatus = (examGroup: Record<string, any>): { canRetake: boolean; message?: string } => {
  // 只对理论试卷进行检查
  if (examGroup.examinationType !== "1") {
    return { canRetake: true };
  }

  const now = new Date();
  const unfinishedBatches =
    examGroup.children?.filter((batch: any) => {
      if (!batch.endDateTime) {
        return false;
      }
      return new Date(batch.endDateTime) > now;
    }) || [];

  if (unfinishedBatches.length > 0) {
    return {
      canRetake: false,
      message: `存在未结束的批次考核，请等待所有批次结束后再设置补考。未结束批次：${unfinishedBatches
        .map((b: any) => `第${b.batchNumber}批`)
        .join("、")}`
    };
  }

  return { canRetake: true };
};

/**
 * @description: 根据补考开始时间判断应该在哪个批次进行补考
 * @param examGroup 考核组数据
 * @param retakeStartTime 补考开始时间
 * @return 目标批次的 examinationRecordID
 */
const getTargetBatchRecordId = (examGroup: Record<string, any>, retakeStartTime: string) => {
  // 考核批次行,默认补考当前批次
  if (!examGroup.children) {
    return examGroup.examinationRecordID;
  }
  const batches =
    examGroup.children
      .filter((batch: any) => batch.startDateTime && batch.endDateTime) // 过滤掉没有时间信息的批次
      ?.sort((a: any, b: any) => new Date(a.startDateTime).getTime() - new Date(b.startDateTime).getTime()) || [];
  if (batches.length === 0) {
    showMessage("warning", "没有可以补考的批次！");
    return;
  }
  const retakeTime = new Date(retakeStartTime);
  for (let i = 0; i < batches.length; i++) {
    const currentBatch = batches[i];
    const nextBatch = batches[i + 1];
    if (!nextBatch) {
      // 当前批次结束后，再能在当前批次的基础上参加补考
      if (currentBatch.endDateTime > datetimeUtil.getNowDate("yyyy-MM-dd hh:mm:ss")) {
        showMessage("warning", "最近批次考试还未结束，不能进行补考");
        return undefined;
      }
      // 最后一个批次，所有后续补考都归到这里
      return currentBatch.examinationRecordID;
    }
    const currentEnd = new Date(currentBatch.endDateTime);
    const nextStart = new Date(nextBatch.startDateTime);
    if (retakeTime >= currentEnd && retakeTime < nextStart) {
      // 在两个批次之间，归到前一个批次
      return currentBatch.examinationRecordID;
    }
  }
  // 默认归到最后一个批次
  if (batches[batches.length - 1].endDateTime > datetimeUtil.getNowDate("yyyy-MM-dd hh:mm:ss")) {
    showMessage("warning", "最近批次考试还未结束，不能进行补考");
    return undefined;
  }
  return batches[batches.length - 1].examinationRecordID;
};

/**
 * @description: 获取补考人员列表
 * @param groupID 考核组ID (第一批考核默认groupID = examinationRecordID)
 */
const getRetakeEmployeeList = async (groupID: string, isGroupRetakeFlag: boolean) => {
  // 按照考核组获取所有需要补考的人员
  if (isGroupRetakeFlag) {
    await examineService.getRetakeEmployeeIDsByGroupID({ groupID, isGroupRetakeFlag }).then((respDatas: any) => {
      retakeExamine.value.employeeIDs = respDatas ?? [];
    });
    return;
  }
  // 按照考核计划获取需要补考的人员
  await examineService.getRetakeEmployeeIDsByRecordID({ examinationRecordID: groupID }).then((respDatas: any) => {
    retakeExamine.value.employeeIDs = respDatas ?? [];
  });
};

/**
 * @description: 打开批量补考对话框
 * @param row 行数据
 */
const openBatchRetakeDialog = async (row: Record<string, any>) => {
  // 1. 检查考核状态
  const statusCheck = checkBatchStatus(row);
  if (!statusCheck.canRetake) {
    showMessage("warning", statusCheck.message || "无法进行补考");
    return;
  }
  // 2. 设置当前操作的考核组数据
  currentExamGroup.value = row;
  // 3. 获取补考人员列表
  await getRetakeEmployeeList(row.groupID, row.examinationRecordID === row.groupID);
  // 4. 重置补考数据（employeeIDs 在 getRetakeEmployeeList 中设置）
  retakeExamine.value = {
    startDateTime: "",
    endDateTime: "",
    employeeIDs: retakeExamine.value.employeeIDs || []
  };

  // 5. 打开补考设置抽屉
  drawerOptions.value.drawerSize = "30%";
  drawerOptions.value.showCancel = true;
  drawerOptions.value.showConfirm = true;
  drawerOptions.value.drawerName = "retakeExamine";
  drawerOptions.value.drawerTitle = `${row.groupName} - 批量补考设置`;
  drawerOptions.value.cancel = () => {
    drawerOptions.value.showDrawer = false;
  };
  drawerOptions.value.confirm = async () => await saveRetakeExamine();
  drawerOptions.value.showDrawer = true;
};

/**
 * @description: 保存批量补考设置
 * @return Promise<boolean>
 */
const saveRetakeExamine = async (): Promise<boolean> => {
  // 校验表单
  if (!(await validateRule(retakeExamineFormRef))) {
    return false;
  }

  // 获取补考的目标批次
  const targetBatchRecordId = getTargetBatchRecordId(currentExamGroup.value!, retakeExamine.value.startDateTime);
  if (!targetBatchRecordId) {
    return false;
  }
  const params = {
    examinationRecordID: targetBatchRecordId,
    startDateTime: retakeExamine.value.startDateTime,
    endDateTime: retakeExamine.value.endDateTime,
    employeeIDs: retakeExamine.value.employeeIDs
  };

  await examineService.saveRetakeExamine(params);
  showMessage("success", "批量补考设置成功！");
  drawerOptions.value.showDrawer = false;
  getPlanSummaryList(); // 刷新数据
  return true;
};

/**
 * @description: 打开详情抽屉
 * @param row 行数据
 */
const openDetailDrawer = (row: Record<string, any>) => {
  selectedPlanRecord.value = row;
  drawerOptions.value.drawerSize = "100%";
  drawerOptions.value.showCancel = false;
  drawerOptions.value.showConfirm = false;
  drawerOptions.value.drawerName = "examinationMainDetail";
  drawerOptions.value.drawerTitle = "考核记录详情";
  drawerOptions.value.showDrawer = true;
};

/**
 * @description: 获取时间选择禁用区间
 * @param time 当前选择的日期
 * @param type 标识是限制开始时间还是结束时间的方法
 * @return boolean
 */
const disabledDate = (time: Date, type: string) => {
  const date = datetimeUtil.formatDate(time, "yyyy-MM-dd");
  if (type === "start") {
    if (!retakeExamine.value.endDateTime) {
      return date < datetimeUtil.getNowDate("yyyy-MM-dd");
    }
    return date < datetimeUtil.getNowDate("yyyy-MM-dd") || date > datetimeUtil.formatDate(retakeExamine.value.endDateTime, "yyyy-MM-dd");
  }
  if (type === "end") {
    if (!retakeExamine.value.startDateTime) {
      return date < datetimeUtil.getNowDate("yyyy-MM-dd");
    }
    return date < datetimeUtil.formatDate(retakeExamine.value.startDateTime, "yyyy-MM-dd");
  }
  return false;
};
// #endregion

// #region 导出excel相关逻辑
const exportExcelOption = ref<ExportExcelView[]>([]);
// 导出Excel列配置
const exportExcelColumns = reactive({
  groupName: "考核组",
  examineName: "考核名称",
  examinationLevelName: "考核级别",
  examinerName: "监考人",
  shouldParticipateCount: "应参加人数",
  participateCount: "参加人数",
  notParticipateCount: "未参加人数",
  passCount: "及格人数/达标人数",
  participationRate: "参考率(%)",
  passRate: "合格率(%)",
  centralExamParticipationRate: "集中考核参加率(%)",
  firstExamPassRate: "首次考核合格率(%)"
});
/**
 * @description: 创建导出Excel参数
 */
const createExportExcelParam = () => {
  exportExcelOption.value = [];
  let cloneData = common.clone(allPlanSummaryList.value);
  exportExcelOption.value.push({
    buttonName: "导出数据",
    fileName: `考核计划汇总${datetimeUtil.getNow("yyyyMMddhhmm")}`,
    sheetName: "考核计划汇总",
    columnData: exportExcelColumns,
    tableData: cloneData
  });
};
// #endregion

// #region 分页逻辑
/**
 * @description: 切换页数配置
 * @param val
 * @return
 */
const handleSizeChange = (val: any) => {
  pageSize.value = val;
  planSummaryList.value = currentChangePage();
};
/**
 * @description: 切页
 * @param val
 * @return
 */
const handleCurrentChange = (val: any) => {
  currentPage.value = val;
  planSummaryList.value = currentChangePage();
};
/**
 * @description: 分页
 * @param size
 * @param current
 * @return
 */
const currentChangePage = () => {
  return allPlanSummaryList.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
};
// #endregion
</script>
<style lang="scss">
.examination-plan-summary {
  .header-date {
    width: 300px;
    margin-right: 20px;
  }
  .export-excel {
    float: right;
  }
  // 展开行样式
  .expand-content {
    padding: 20px;
    background-color: #f8f9fa;

    h4 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .expand-table {
      .el-table__header {
        th {
          background-color: #e9ecef !important;
        }
      }
    }
  }
}
</style>
