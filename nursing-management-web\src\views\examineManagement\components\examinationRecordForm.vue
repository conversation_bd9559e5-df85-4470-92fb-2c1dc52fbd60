<!--
 * relative     : \nursing-management-web\src\views\examineManagement\components\examinationRecordForm.vue
 * Author       : 张现忠
 * Date         : 2025-03-30 15:05
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-15 15:28
 * Description  : 考核计划add/edit页面
 * CodeIterationRecord:
 -->

<template>
  <el-form
    :model="formData"
    ref="submitRefs"
    class="examination-record-form"
    :label-width="examinationType === paperType.exerciseType ? 80 : 140"
    :rules="rules"
  >
    <el-form-item label="考核类型：" prop="type">
      <examination-type-selector label="" :width="300" v-model="formData.type" :type="routeType" disabled></examination-type-selector>
    </el-form-item>
    <el-form-item v-if="examinationType === paperType.exerciseType" label="选择题库：" prop="questionBankID">
      <el-select class="drawer-select" v-model="formData.questionBankID" placeholder="请选择题库" filterable>
        <el-option v-for="item in questionBanks" :key="item.label" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </el-form-item>
    <template v-else>
      <el-form-item label="试卷：" prop="examinationPaperMainID">
        <el-select class="drawer-select" v-model="formData.examinationPaperMainID" filterable remote @change="getChoosePaper">
          <el-option v-for="item in paperMainOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="考核名称：" prop="examinationName">
        <el-input class="from-input" v-model="formData.examinationName" placeholder="请输入考核名称"></el-input>
      </el-form-item>
      <el-form-item label="考核级别：" prop="examinationLevel">
        <examination-level-selector
          label=""
          :width="300"
          v-model="formData.examinationLevel"
          @change="formData.departmentID = undefined"
        ></examination-level-selector>
      </el-form-item>
      <el-form-item label="部门：" prop="departmentID">
        <!-- examinationLevel=1（病区考核）时取organizationType=1（部门）
         examinationLevel=2（院级考核）时取organizationType=2（学组） -->
        <department-selector
          label=""
          v-model="formData.departmentID"
          :organizationType="formData.examinationLevel"
          :employeeID="userStore.employeeID"
          :width="300"
        ></department-selector>
      </el-form-item>
      <el-form-item :label="examineEmployeeLabel" prop="examineEmployeeID">
        <employee-selector
          label=""
          v-model="formData.examineEmployeeID"
          :width="300"
          :filterable="true"
          :multiple="examinationType === paperType.practicalType"
          :multiCollapse="false"
          :clear="false"
        />
      </el-form-item>
      <el-form-item v-if="examinationType != paperType.simulationType" label="参加考核人员：" prop="conditionExpression">
        <condition-input
          ref="conditionInputRef"
          title="设置参加考核人员条件"
          showStyle
          :width="300"
          clearable
          :content="formData.conditionContent"
          :defaultValue="formData.conditions"
          :selectComponent="selectComponent"
          @result="setConditionData($event)"
          @clear="clearConditionData()"
          :allowMultiLevel="false"
        ></condition-input>
      </el-form-item>
      <template v-if="examinationType !== paperType.simulationType">
        <el-form-item label="开始时间：" prop="startDateTime">
          <el-date-picker
            class="drawer-select"
            v-model="formData.startDateTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            placeholder="选择开始时间"
            :disabled-date="(date: Date) => disabledDate(date, 'start')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间：" prop="endDateTime">
          <el-date-picker
            class="drawer-select"
            v-model="formData.endDateTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            placeholder="选择结束时间"
            :disabled-date="(date: Date) => disabledDate(date, 'end')"
          ></el-date-picker>
        </el-form-item>
      </template>
      <el-form-item label="考核地点：" prop="location">
        <el-input v-model="formData.location" />
      </el-form-item>
      <el-form-item :label="examinationType === paperType.practicalType ? '操作时长（分钟)：' : '考核时长（分钟)：'" prop="duration">
        <el-input-number v-model="formData.duration" :step="1" :min="0" />
      </el-form-item>
      <template v-if="examinationType !== paperType.practicalType">
        <el-form-item label="最少答卷时长（分钟)：" prop="minAnswerTime">
          <el-input-number v-model="formData.minAnswerTime" :step="1" :min="0" :max="formData.duration" />
        </el-form-item>
        <template v-if="examinationType !== paperType.simulationType">
          <el-form-item label="一页一题：" prop="signInFlag">
            <el-checkbox v-model="formData.onePageQuestionFlag" />
          </el-form-item>
          <el-form-item label="扫码签到：" prop="signInFlag">
            <el-checkbox v-model="formData.signInFlag" />
          </el-form-item>
          <el-form-item v-if="formData.signInFlag" label="签到码刷新时间（秒)：" prop="qrCodeRefreshTime">
            <el-input-number v-model="formData.qrCodeRefreshTime" :step="1" :min="0" />
            <div class="form-item-hint">0为不刷新</div>
          </el-form-item>
        </template>
      </template>
      <el-form-item label="说明：" prop="instructions">
        <el-input v-model="formData.instructions" autosize type="textarea" placeholder="请输入试卷说明" />
      </el-form-item>
    </template>
  </el-form>
</template>

<script setup lang="ts">
import examinationLevelSelector from "./examinationLevelSelector.vue";
import { PaperType } from "@/utils/enum";
const { userStore } = useStore();

const props = defineProps({
  routeType: {
    type: String,
    required: true
  },
  examinationType: {
    type: String,
    required: true
  },
  // 发布时的试卷ID
  publishExaminePaperMainID: {
    type: String as PropType<string>,
    default: ""
  },
  // 编辑时的数据
  modelValue: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({})
  }
});
const questionBanks = ref<Record<string, any>[]>([]);
const paperMainOption = ref<{ label: string; value: string }[]>([]);
const examineEmployeeLabel = computed(() => {
  if (props.examinationType === paperType.value.theoreticalType) {
    return "监考人：";
  }
  return props.examinationType === paperType.value.practicalType ? "可监考人：" : "考核人：";
});
onBeforeMount(async () => {
  if (props.examinationType === paperType.value.exerciseType) {
    await examineService.getQuestionBankSelectList({ isPractical: false }).then((res: any) => {
      questionBanks.value = res;
    });
  } else {
    await getPaperMainSelectList(props.examinationType);
  }
});
onMounted(async () => {
  await getConditionSetting();
  initFormData();
});
defineEmits(["update:modelValue"]);

const submitRefs = shallowRef();
const { validateRule } = useForm();
const formData = ref<Record<string, any>>({});
const conditionInputRef = ref<any>();
const rules = {
  type: [{ required: true, message: "请选择考核类型", trigger: "change" }],
  examinationPaperMainID: [{ required: true, message: "请选择试卷", trigger: "change" }],
  questionBankID: [{ required: true, message: "请选择题库", trigger: "change" }],
  examinationName: [{ required: true, message: "请输入考核名称", trigger: "change" }],
  departmentID: [{ required: true, message: "请选择部门", trigger: "change" }],
  examinationLevel: [{ required: true, message: "请选择考核级别", trigger: "change" }],
  examineEmployeeID: [{ required: true, message: "请选择考核人员", trigger: "change" }],
  startDateTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endDateTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  duration: [{ required: true, message: "请输入考核时长", trigger: "change" }],
  minAnswerTime: [{ required: true, message: "请输入最少答卷时长", trigger: "change" }],
  conditionExpression: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (conditionInputRef.value.validator(formData.value.conditionContent)) {
          callback();
          return;
        }
        callback("请设置参加考核人员");
      }
    }
  ]
};
const paperType = computed(() => {
  return PaperType;
});
/**
 * @description: 初始化表单数据
 */
const initFormData = () => {
  if (props.modelValue && Object.keys(props.modelValue).length > 0) {
    formData.value = props.modelValue as any;
    return;
  }
  formData.value = {
    type: props.examinationType,
    examinationPaperMainID: "",
    questionBankID: "",
    examinationName: "",
    examineEmployeeID: props.examinationType === PaperType.practicalType ? [userStore.employeeID] : userStore.employeeID,
    departmentID: userStore.departmentID,
    conditionExpression: "",
    conditionContent: "",
    conditions: [],
    duration: undefined,
    minAnswerTime: undefined,
    // 理论试卷需要签到
    signInFlag: props.examinationType === PaperType.theoreticalType,
    qrCodeRefreshTime: 0,
    publishFlag: [PaperType.exerciseType.toString(), PaperType.simulationType.toString()].includes(props.examinationType),
    instructions: "",
    examinationLevel: "1",
    onePageQuestionFlag: props.examinationType === PaperType.exerciseType
  };
  if (props.publishExaminePaperMainID) {
    formData.value.examinationPaperMainID = props.publishExaminePaperMainID;
  }
  // 模拟考试 设置默认考核时间,默认七天
  if (props.examinationType === PaperType.simulationType) {
    formData.value.startDateTime = datetimeUtil.getNow("yyyy-MM-dd hh:mm");
    formData.value.endDateTime = datetimeUtil.addDate(datetimeUtil.getNow(), 7, "yyyy-MM-dd hh:mm");
  }
  getChoosePaper();
};
/**
 * @description: 设置好的条件赋值给组件属性
 * @param filterData
 * @return
 */
const setConditionData = (filterData: Record<string, any>) => {
  formData.value.conditions = filterData.filterConditions;
  formData.value.conditionContent = filterData.filterConditionContent;
  formData.value.conditionExpression = filterData.filterConditionExpression;
};
/**
 * @description: 清空组件属性中的条件
 */
const clearConditionData = () => {
  formData.value.conditions = [];
  formData.value.conditionContent = "";
  formData.value.conditionExpression = "";
};
/**
 * @description: 试卷下拉框change事件
 */
const getChoosePaper = () => {
  let choosePaper = paperMainOption.value.find((paper: Record<string, any>) => paper.value === formData.value.examinationPaperMainID);
  if (choosePaper) {
    formData.value.examinationName = choosePaper.label;
  }
};
const selectComponent = ref<Record<string, any>>({});

/**
 * @description: 获取发布条件
 */
const getConditionSetting = async () => {
  let params = {
    type: "ExaminationRecord"
  };
  await conditionService.getConditionSelectComponent(params).then((res: any) => {
    selectComponent.value = res;
  });
};
/**
 * @description: 获取试卷下拉框数据
 */
const getPaperMainSelectList = async (type: string) => {
  let params = {
    type: type
  };
  await examineService.getPaperMainSelectList(params).then((res: any) => {
    if (res) {
      paperMainOption.value = res;
    }
  });
};
/**
 * @description: 获取时间选择禁用区间
 * @param date 当前选择的日期
 * @param flag {"start" | "end"} 标识是限制开始时间还是结束时间的方法
 * @return
 */
const disabledDate = (date: Date, flag: "start" | "end") => {
  if (flag === "start") {
    return datetimeUtil.formatDate(date, "yyyy-MM-dd") < datetimeUtil.getNowDate();
  }
  // 选择考核结束时间时，限制结束时间不能小于开始时间
  if (formData.value.startDateTime) {
    return datetimeUtil.formatDate(date, "yyyy-MM-dd") < datetimeUtil.formatDate(formData.value.startDateTime, "yyyy-MM-dd");
  }
};

/**
 * @description: 保存考核主记录
 */
const saveRecord = async (isPublish: boolean = false) => {
  // 校验表单
  if (!(await validateRule(submitRefs))) {
    return false;
  }
  let result = false;
  if (formData.value.type === paperType.value.exerciseType.toString()) {
    const questionBankID = formData.value.questionBankID;
    const questionBankName = questionBanks.value.find((questionBank) => questionBank.value === questionBankID)?.label ?? "";
    await examineService.savePracticeExamination({ questionBankID, questionBankName }).then((res) => {
      if (res) {
        result = true;
      }
    });
  } else {
    result = await saveRecordData(isPublish);
  }
  return result;
};

/**
 * @description: 保存考核主记录数据
 */
const saveRecordData = async (isPublish: boolean = false) => {
  let cloneData = common.clone(formData.value);
  cloneData.examineEmployeeID = [].concat(cloneData.examineEmployeeID as any);
  if (isPublish) {
    cloneData.publishFlag = true;
  }
  let result = false;
  await examineService.saveExaminationRecordData(cloneData).then((res) => {
    result = Boolean(res);
  });
  return result;
};
defineExpose({
  formData,
  submitRefs,
  saveRecord
});
</script>

<style lang="scss">
.examination-record-form {
  padding: 0;
  height: 100%;
  .drawer-select {
    width: 300px;
  }
  .from-input {
    width: 300px;
  }
  .form-item-hint {
    color: #999;
    font-size: 12px;
    margin-top: 5px;
  }
}
</style>
